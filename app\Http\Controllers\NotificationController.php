<?php

namespace App\Http\Controllers;

use App\Facades\PushNotification;
use App\Models\User;
use App\Services\PushNotification\PushNotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NotificationController extends Controller
{
    protected PushNotificationService $pushNotificationService;

    public function __construct(PushNotificationService $pushNotificationService)
    {
        $this->pushNotificationService = $pushNotificationService;
    }

    /**
     * Send notification to a specific user
     */
    public function sendToUser(Request $request): JsonResponse
    {
        $request->validate([
            'user_phone' => 'required|exists:users,phone',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'data' => 'sometimes|array',
        ]);

        $user = User::where('phone', $request->user_phone)->firstOrFail();

        $result = $this->pushNotificationService->sendToUser(
            $user,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Notification sent successfully' : 'Failed to send notification',
            'data' => $result,
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Send notification to multiple users
     */
    public function sendToUsers(Request $request): JsonResponse
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'data' => 'sometimes|array',
        ]);

        $users = User::whereIn('id', $request->user_ids)->get();

        $result = $this->pushNotificationService->sendToUsers(
            $users,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Notifications sent successfully' : 'Failed to send notifications',
            'data' => $result,
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Send notification to a device token directly
     */
    public function sendToToken(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required|string',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'data' => 'sometimes|array',
        ]);

        $result = $this->pushNotificationService->sendToToken(
            $request->token,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Notification sent successfully' : 'Failed to send notification',
            'data' => $result,
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Send notification to a topic
     */
    public function sendToTopic(Request $request): JsonResponse
    {
        $request->validate([
            'topic' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'data' => 'sometimes|array',
        ]);

        $result = $this->pushNotificationService->sendToTopic(
            $request->topic,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Notification sent successfully' : 'Failed to send notification',
            'data' => $result,
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Validate a notification token
     */
    public function validateToken(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required|string',
        ]);

        $isValid = $this->pushNotificationService->validateToken($request->token);

        return response()->json([
            'valid' => $isValid,
            'message' => $isValid ? 'Token is valid' : 'Token is invalid',
        ]);
    }

    /**
     * Example using the facade
     */
    public function sendUsingFacade(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'data' => 'sometimes|array',
        ]);

        $user = User::findOrFail($request->user_id);

        $result = PushNotification::sendToUser(
            $user,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Notification sent successfully via facade' : 'Failed to send notification',
            'data' => $result,
        ], $result['success'] ? 200 : 400);
    }
}
