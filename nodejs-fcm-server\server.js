const express = require('express');
const admin = require('firebase-admin');
const crypto = require('crypto');
const cors = require('cors');
const helmet = require('helmet');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Secret key for HMAC signature (should match Flutter app)
const SECRET_KEY = process.env.SECRET_KEY || 'superSecretKey123';

// Initialize Firebase Admin SDK
const initializeFirebase = () => {
  try {
    // Option 1: Using service account file
    if (process.env.FIREBASE_SERVICE_ACCOUNT_PATH) {
      const serviceAccount = require(process.env.FIREBASE_SERVICE_ACCOUNT_PATH);
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID
      });
    }
    // Option 2: Using environment variables
    else if (process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL) {
      const serviceAccount = {
        type: "service_account",
        project_id: process.env.FIREBASE_PROJECT_ID,
        private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
        private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
        client_id: process.env.FIREBASE_CLIENT_ID,
        auth_uri: "https://accounts.google.com/o/oauth2/auth",
        token_uri: "https://oauth2.googleapis.com/token",
        auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
        client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
      };
      
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID
      });
    } else {
      throw new Error('Firebase credentials not properly configured');
    }
    
    console.log('Firebase Admin SDK initialized successfully');
  } catch (error) {
    console.error('Error initializing Firebase:', error.message);
    process.exit(1);
  }
};

// Generate HMAC-SHA256 signature
const generateSignature = (phoneNumber, message, timestamp) => {
  const data = phoneNumber + message + timestamp;
  return crypto.createHmac('sha256', SECRET_KEY).update(data).digest('hex');
};

// Validate request data
const validateRequest = (req, res, next) => {
  const { deviceToken, phoneNumber, message } = req.body;
  
  if (!deviceToken || typeof deviceToken !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'deviceToken is required and must be a string'
    });
  }
  
  if (!phoneNumber || typeof phoneNumber !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'phoneNumber is required and must be a string'
    });
  }
  
  if (!message || typeof message !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'message is required and must be a string'
    });
  }
  
  next();
};

// Main endpoint for sending special SMS notifications
app.post('/sendSpecialSms', validateRequest, async (req, res) => {
  try {
    const { deviceToken, phoneNumber, message } = req.body;
    
    // Generate timestamp (Unix seconds)
    const timestamp = Math.floor(Date.now() / 1000).toString();
    
    // Generate signature
    const signature = generateSignature(phoneNumber, message, timestamp);
    
    // Prepare FCM message with data payload
    const fcmMessage = {
      token: deviceToken,
      data: {
        type: 'special_sms',
        phoneNumber: phoneNumber,
        message: message,
        timestamp: timestamp,
        signature: signature
      },
      // Optional: Add notification for display
      notification: {
        title: 'Special SMS',
        body: `Message from ${phoneNumber}`
      },
      // Android specific options
      android: {
        priority: 'high',
        data: {
          type: 'special_sms',
          phoneNumber: phoneNumber,
          message: message,
          timestamp: timestamp,
          signature: signature
        }
      },
      // iOS specific options
      apns: {
        payload: {
          aps: {
            'content-available': 1,
            sound: 'default'
          }
        }
      }
    };
    
    // Send FCM notification
    const fcmResponse = await admin.messaging().send(fcmMessage);
    
    // Return success response
    res.json({
      success: true,
      message: 'Special SMS notification sent successfully',
      data: {
        fcmResponse: fcmResponse,
        signature: signature,
        timestamp: parseInt(timestamp),
        phoneNumber: phoneNumber,
        messageLength: message.length
      }
    });
    
  } catch (error) {
    console.error('Error sending FCM notification:', error);
    
    // Handle specific FCM errors
    let errorMessage = 'Failed to send notification';
    let statusCode = 500;
    
    if (error.code === 'messaging/invalid-registration-token') {
      errorMessage = 'Invalid device token';
      statusCode = 400;
    } else if (error.code === 'messaging/registration-token-not-registered') {
      errorMessage = 'Device token not registered';
      statusCode = 400;
    } else if (error.code === 'messaging/invalid-argument') {
      errorMessage = 'Invalid message format';
      statusCode = 400;
    }
    
    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      details: error.message
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'FCM Special SMS Server'
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Initialize Firebase and start server
initializeFirebase();

app.listen(PORT, () => {
  console.log(`FCM Special SMS Server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`Special SMS endpoint: http://localhost:${PORT}/sendSpecialSms`);
});

module.exports = app;
